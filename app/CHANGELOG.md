# Changelog

This changelog references changes made both to the FastAPI backend, `fastapi_backend`, and the
frontend TypeScript client, `nextjs-frontend`.

!!! note
    The backend and the frontend are versioned together, that is, they have the same version number.
    When you update the backend, you should also update the frontend to the same version.

## 0.0.5 <small>July 9, 2025</small> {id="0.0.5"}

- Items Pagination

## 0.0.4 <small>July 9, 2025</small> {id="0.0.4"}

- Fix <PERSON><PERSON> missing for pre-commit

## 0.0.3 <small>April 23, 2025</small> {id="0.0.3"}

- Created docs

## 0.0.2 <small>March 12, 2025</small> {id="0.0.2"}

- Generate release draft using github actions

## 0.0.1 <small>March 12, 2025</small> {id="0.0.1"}

- Initial release
