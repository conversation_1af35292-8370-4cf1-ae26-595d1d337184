["tests/commands/test_generate_openapi_schema.py::test_generate_openapi_schema", "tests/commands/test_generate_openapi_schema.py::test_remove_operation_id_tag", "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-Nosppecialchar1-400-expected_detail3]", "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-lowercasepassword-400-expected_detail2]", "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-short-400-expected_detail0]", "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-shorttest-400-expected_detail4]", "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL><EMAIL>-400-expected_detail1]", "tests/main/test_main.py::TestPasswordValidation::test_register_user_with_valid_password", "tests/routes/test_items.py::TestItems::test_create_item", "tests/routes/test_items.py::TestItems::test_delete_item", "tests/routes/test_items.py::TestItems::test_delete_nonexistent_item", "tests/routes/test_items.py::TestItems::test_read_items", "tests/routes/test_items.py::TestItems::test_unauthorized_create_item", "tests/routes/test_items.py::TestItems::test_unauthorized_delete_item", "tests/routes/test_items.py::TestItems::test_unauthorized_read_items", "tests/test_database.py::test_create_db_and_tables", "tests/test_database.py::test_engine_creation", "tests/test_database.py::test_get_async_session", "tests/test_database.py::test_get_user_db", "tests/test_database.py::test_session_maker_configuration", "tests/test_email.py::test_get_email_config", "tests/test_email.py::test_send_reset_password_email", "tests/utils/test_utils.py::test_simple_generate_unique_route_id"]