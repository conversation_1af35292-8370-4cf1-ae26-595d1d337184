{"tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-short-400-expected_detail0]": true, "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL><EMAIL>-400-expected_detail1]": true, "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-lowercasepassword-400-expected_detail2]": true, "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-Nosppecialchar1-400-expected_detail3]": true, "tests/main/test_main.py::TestPasswordValidation::test_password_validation[<EMAIL>-shorttest-400-expected_detail4]": true, "tests/main/test_main.py::TestPasswordValidation::test_register_user_with_valid_password": true, "tests/routes/test_items.py::TestItems::test_create_item": true, "tests/routes/test_items.py::TestItems::test_read_items": true, "tests/routes/test_items.py::TestItems::test_delete_item": true, "tests/routes/test_items.py::TestItems::test_delete_nonexistent_item": true, "tests/routes/test_items.py::TestItems::test_unauthorized_read_items": true, "tests/routes/test_items.py::TestItems::test_unauthorized_create_item": true, "tests/routes/test_items.py::TestItems::test_unauthorized_delete_item": true}