{"name": "template-app-frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate-client": "openapi-ts", "test": "jest", "coverage": "jest --coverage", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx,json,css,html}'", "type-check": "tsc --noEmit"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hey-api/client-fetch": "0.13.1", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.6.1", "lucide-react": "^0.452.0", "next": "15.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.33.0", "@hey-api/openapi-ts": "^0.80.10", "@next/eslint-plugin-next": "^15.4.6", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^20.19.10", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@typescript-eslint/eslint-plugin": "^8.39.1", "autoprefixer": "^10.4.21", "chokidar": "^4.0.3", "chokidar-cli": "^3.0.0", "eslint": "^9.33.0", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^10.1.8", "fork-ts-checker-webpack-plugin": "^9.1.0", "globals": "^15.15.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2", "typescript-eslint": "^8.39.1"}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}