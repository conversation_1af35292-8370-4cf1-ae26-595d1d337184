fail_fast: true

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
        args: ["--maxkb=500"]
        exclude: >
          (?x)^(
              package-lock\.json
          )$
      - id: fix-byte-order-marker
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: debug-statements
      - id: detect-private-key

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.12.2
    hooks:
      # Run the linter.
      - id: ruff
        args: [--fix]
      # Run the formatter.
      - id: ruff-format

  - repo: local
    hooks:
      - id: frontend-lint
        name: run frontend lint
        entry: sh -c 'cd nextjs-frontend && npm run lint'
        language: system
        types: [ file ]
        files: ^nextjs-frontend/.*\.(js|jsx|ts|tsx)$
        pass_filenames: true
      - id: frontend-prettier
        name: Run Prettier on frontend files
        entry: sh -c 'cd nextjs-frontend && pnpm run prettier'
        language: system
        types: [ file ]
        files: ^nextjs-frontend/.*\.(js|jsx|ts|tsx)$
        pass_filenames: true
      - id: frontend-tsc
        name: run frontend tsc
        entry: sh -c 'cd nextjs-frontend && pnpm run tsc'
        language: system
        types: [ file ]
        files: ^nextjs-frontend/.*\.(ts|tsx)$
        pass_filenames: false
      - id: generate-openapi-schema
        name: generate OpenAPI schema
        entry: sh -c 'cd fastapi_backend && uv run python -m commands.generate_openapi_schema'
        language: system
        # Only run OpenAPI schema generation if schemas.py, main.py or package version have changed:
        files: (main\.py$|schemas\.py$|pyproject\.toml)
        pass_filenames: false
      - id: generate-frontend-client
        name: generate frontend client
        entry: sh -c 'cd nextjs-frontend && pnpm run generate-client'
        language: system
        # Only run frontend client generation if frontend files have changed:
        files: openapi\.json$
        pass_filenames: false