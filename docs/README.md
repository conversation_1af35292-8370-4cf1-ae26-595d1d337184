# Project Documentation Wiki

This repository ships with a complete, structured documentation system. Use this README as your index and "how to"
guide.

## How to use this wiki

- Follow the 10-phase methodology in order (00 → 09)
- Copy templates and checklists into your project docs, then tailor them
- Keep docs as the single source of truth; update them alongside code

## Index

Core notebooks (start here):

- 00 — Overview: docs/core/00-overview.md
- 01 — Product: docs/core/01-product.md
- 02 — Structure: docs/core/02-structure.md
- 03 — Tech: docs/core/03-tech.md
- 04 — Rules: docs/core/04-rules.md
- 05 — Requirements: docs/core/05-requirements.md
- 06 — Design: docs/core/06-design.md
- 07 — Tasks: docs/core/07-tasks.md
- 08 — Workflows: docs/core/08-workflows.md
- 09 — Release: docs/core/09-release.md

Operations and delivery:

- Deployment Playbook: docs/deployment/README.md
- Operations Runbook: docs/operations/README.md
- Tasks Management: docs/tasks/README.md

## Generating structure reports

Use the scanner to export a repository tree into docs/:

- uv run python scripts/scan-structure.py -f md json
- Outputs docs/codebase-structure.md and docs/codebase-structure.json

## Conventions

- Formatting: see prettier.config.js (Markdown: width 120, proseWrap always)
- Backends use uv; frontends use pnpm
- Quality gates: see docs/core/04-rules.md and scripts/quality-check.sh
