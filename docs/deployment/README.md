# Deployment Playbook Template

Use this checklist-driven guide to plan, execute, and verify deployments safely and repeatably.

## Environments

- List all environments (dev, staging, production)
- For each: URL, access, infra summary, secrets location, data policies

## Release Artifacts

- Artifact(s) to deploy (images, bundles, migrations)
- Versions, checksums, build provenance
- Release notes link / CHANGELOG section

## Pre-deployment Checklist

- [ ] Code merged and tagged
- [ ] CI green and artifacts published
- [ ] Infra ready: capacity, feature flags, config toggles
- [ ] DB migrations prepared and reviewed
- [ ] Rollback plan drafted and tested
- [ ] Stakeholders notified and change window approved

## Deployment Strategy

- Strategy: Blue/Green, Canary, Rolling, or Manual
- Steps with commands (parameterize by env)
- Expected timelines and health signals

## Database Migrations

- Order of operations (pre, during, post)
- Backward compatibility plan (expand/contract)
- Dry-run steps and verification

## Verification & Health Checks

- SLOs/SLIs and thresholds
- Dashboards and queries to watch
- Synthetic probes and smoke tests
- Manual QA checklist

## Rollback Plan

- Trigger conditions and decision thresholds
- Rollback steps with commands
- Data considerations (migrations/data fix)
- Post-rollback validation

## Post-deployment

- [ ] Announce success and version
- [ ] Enable features gradually (if flagged)
- [ ] Capture metrics deltas and error budgets
- [ ] Create/close tickets and attach evidence

## Runbook Links

- Incident response guide (docs/operations/README.md)
- Release process (docs/core/09-release.md)
