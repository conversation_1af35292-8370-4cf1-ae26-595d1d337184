# Operations Runbook Template

A practical guide for operating the system in production.

## Service Overview

- System diagram link and ownership
- Dependencies and external services
- Environments and key URLs

## SLOs, SLIs, and Error Budgets

- SLOs (availability, latency, quality)
- SLIs definitions and measurement
- Error budget policy and burn alerts

## Monitoring & Alerting

- Dashboards links and panels
- Alert rules with thresholds and runbooks
- Logging conventions and query examples

## On-Call & Escalation

- Rotation schedule and contact methods
- Escalation ladder and time targets
- Paging policies (quiet hours, ack time)

## Routine Operations

- Common procedures: restarts, scaling, cache flush
- Data tasks: backups, restores, migrations
- Access management and secrets rotation

## Incident Response

1. Triage
   - Severity classification (Sev1–Sev4)
   - Initial comms and roles (IC, Ops, Comms)
2. Stabilize
   - Mitigation steps and feature flags
   - Rollback criteria (link to deployment playbook)
3. Diagnose
   - Logs, metrics, traces, known issues
4. Communicate
   - Status page/update cadence and audience

## Postmortem Template (Blameless)

- Summary: What happened and impact
- Timeline: Key events and timestamps
- Root Cause: Technical and contributing factors
- What Went Well / Where We Struggled
- Action Items: Owner, priority, due date
- Follow-up: Verification of fixes and learning share-out

## References

- Deployment: docs/deployment/README.md
- Release: docs/core/09-release.md
