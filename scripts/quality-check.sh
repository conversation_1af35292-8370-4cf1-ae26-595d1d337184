#!/bin/bash

# Templates - Local Quality Gates Script
# Comprehensive quality checks for local development
# Usage: ./scripts/quality-check.sh [--backend|--frontend|--all] [--fix] [--verbose]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/server"
FRONTEND_DIR="$PROJECT_ROOT/client"

# Flags
CHECK_BACKEND=false
CHECK_FRONTEND=false
AUTO_FIX=false
VERBOSE=false
FAILED_CHECKS=()

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend)
                CHECK_BACKEND=true
                shift
                ;;
            --frontend)
                CHECK_FRONTEND=true
                shift
                ;;
            --all)
                CHECK_BACKEND=true
                CHECK_FRONTEND=true
                shift
                ;;
            --fix)
                AUTO_FIX=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}Unknown option: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done

    # Default to checking all if nothing specified
    if [[ "$CHECK_BACKEND" == false && "$CHECK_FRONTEND" == false ]]; then
        CHECK_BACKEND=true
        CHECK_FRONTEND=true
    fi
}

show_help() {
    echo "Ultimate Electrical Designer - Quality Gates Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --backend     Run backend quality checks only"
    echo "  --frontend    Run frontend quality checks only"
    echo "  --all         Run all quality checks (default)"
    echo "  --fix         Automatically fix issues where possible"
    echo "  --verbose     Show detailed output"
    echo "  --help, -h    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all checks"
    echo "  $0 --backend --fix    # Run backend checks with auto-fix"
    echo "  $0 --frontend         # Run frontend checks only"
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [[ "$VERBOSE" == true ]]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Run command with error handling
run_check() {
    local check_name="$1"
    local command="$2"
    local fix_command="$3"
    local directory="$4"

    log_info "Running $check_name..."
    
    if [[ -n "$directory" ]]; then
        cd "$directory"
    fi

    if [[ "$VERBOSE" == true ]]; then
        log_verbose "Command: $command"
        log_verbose "Directory: $(pwd)"
    fi

    if eval "$command" >/dev/null 2>&1; then
        log_success "$check_name passed"
        return 0
    else
        log_error "$check_name failed"
        FAILED_CHECKS+=("$check_name")

        if [[ "$AUTO_FIX" == true && -n "$fix_command" ]]; then
            log_info "Attempting to fix $check_name..."
            if eval "$fix_command" >/dev/null 2>&1; then
                log_success "$check_name fixed"
                # Re-run the check to verify fix
                if eval "$command" >/dev/null 2>&1; then
                    log_success "$check_name passed after fix"
                    # Remove from failed checks
                    FAILED_CHECKS=("${FAILED_CHECKS[@]/$check_name}")
                    return 0
                else
                    log_error "$check_name still failing after fix attempt"
                fi
            else
                log_error "Failed to fix $check_name"
            fi
        fi
        return 1
    fi
}

# Backend quality checks
run_backend_checks() {
    log_info "Starting backend quality checks..."
    
    cd "$BACKEND_DIR"

    # Check if uv is available
    if ! command_exists uv; then
        log_error "uv not found. Please install uv first."
        return 1
    fi

    # Code formatting (Ruff)
    run_check \
        "Backend Code Formatting" \
        "uv run ruff format . --check" \
        "uv run ruff format ." \
        "$BACKEND_DIR"

    # Linting (Ruff)
    run_check \
        "Backend Linting" \
        "uv run ruff check ." \
        "uv run ruff check . --fix" \
        "$BACKEND_DIR"

    # Type checking (MyPy)
    run_check \
        "Backend Type Checking" \
        "uv run mypy src/ --show-error-codes" \
        "" \
        "$BACKEND_DIR"

    # Security scanning (Bandit)
    run_check \
        "Backend Security Scan" \
        "uv run bandit -r src/ -q" \
        "" \
        "$BACKEND_DIR"

    # Unit tests
    if [[ "$VERBOSE" == true ]]; then
        run_check \
            "Backend Unit Tests" \
            "uv run pytest -v -m 'not integration and not performance' --tb=short" \
            "" \
            "$BACKEND_DIR"
    else
        run_check \
            "Backend Unit Tests" \
            "uv run pytest -q -m 'not integration and not performance'" \
            "" \
            "$BACKEND_DIR"
    fi

    log_info "Backend quality checks completed"
}

# Frontend quality checks
run_frontend_checks() {
    log_info "Starting frontend quality checks..."
    
    cd "$FRONTEND_DIR"

    # Check if pnpm is available
    if ! command_exists pnpm; then
        log_error "pnpm not found. Please install pnpm first."
        return 1
    fi

    # Check if node_modules exists
    if [[ ! -d "node_modules" ]]; then
        log_warning "node_modules not found. Installing dependencies..."
        pnpm install
    fi

    # TypeScript compilation
    run_check \
        "Frontend TypeScript Compilation" \
        "pnpm tsc --noEmit" \
        "" \
        "$FRONTEND_DIR"

    # Code formatting (Prettier)
    run_check \
        "Frontend Code Formatting" \
        "pnpm prettier --check \"**/*.{ts,tsx,mdx}\"" \
        "pnpm prettier --write \"**/*.{ts,tsx,mdx}\"" \
        "$FRONTEND_DIR"

    # Linting (ESLint)
    run_check \
        "Frontend Linting" \
        "pnpm next lint" \
        "pnpm next lint --fix" \
        "$FRONTEND_DIR"

    # Unit tests
    if [[ "$VERBOSE" == true ]]; then
        run_check \
            "Frontend Unit Tests" \
            "pnpm vitest run --reporter=verbose" \
            "" \
            "$FRONTEND_DIR"
    else
        run_check \
            "Frontend Unit Tests" \
            "pnpm vitest run --reporter=basic" \
            "" \
            "$FRONTEND_DIR"
    fi

    # Build check
    run_check \
        "Frontend Build Check" \
        "pnpm build" \
        "" \
        "$FRONTEND_DIR"

    log_info "Frontend quality checks completed"
}

# Generate summary report
generate_summary() {
    echo ""
    echo "=================================================="
    echo "           QUALITY GATES SUMMARY"
    echo "=================================================="
    echo ""

    if [[ ${#FAILED_CHECKS[@]} -eq 0 ]]; then
        log_success "🎉 ALL QUALITY CHECKS PASSED!"
        echo ""
        echo "Your code is ready for commit and deployment."
        echo ""
        return 0
    else
        log_error "❌ ${#FAILED_CHECKS[@]} QUALITY CHECK(S) FAILED:"
        echo ""
        for check in "${FAILED_CHECKS[@]}"; do
            echo "  • $check"
        done
        echo ""
        echo "Please fix the failing checks before committing your code."
        echo ""
        if [[ "$AUTO_FIX" == false ]]; then
            echo "Tip: Use --fix flag to automatically fix some issues."
        fi
        echo ""
        return 1
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "    ULTIMATE ELECTRICAL DESIGNER QUALITY GATES"
    echo "=================================================="
    echo ""

    parse_args "$@"

    log_info "Project root: $PROJECT_ROOT"
    if [[ "$AUTO_FIX" == true ]]; then
        log_info "Auto-fix mode enabled"
    fi
    if [[ "$VERBOSE" == true ]]; then
        log_info "Verbose mode enabled"
    fi
    echo ""

    # Store original directory
    ORIGINAL_DIR=$(pwd)

    # Run checks
    if [[ "$CHECK_BACKEND" == true ]]; then
        run_backend_checks
        echo ""
    fi

    if [[ "$CHECK_FRONTEND" == true ]]; then
        run_frontend_checks
        echo ""
    fi

    # Return to original directory
    cd "$ORIGINAL_DIR"

    # Generate summary
    generate_summary
    exit $?
}

# Run main function with all arguments
main "$@"