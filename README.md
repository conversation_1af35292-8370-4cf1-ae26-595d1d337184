# Templates

A documentation-first project template. It provides a complete docs wiki (00–09), opinionated local quality gates, and
utilities to help you bootstrap new projects consistently. Application code (server/client) is intentionally not
included.

## Contents

- docs/core: 10-phase documentation wiki from Overview to Release
- docs/deployment: Deployment playbook template
- docs/operations: Operations runbook template
- docs/tasks: Task management template
- scripts: Local tooling (quality gates, structure scan)
- prettier.config.js: Global formatting for Markdown

## Quickstart

Prerequisites

- Python tooling: uv
- Frontend tooling: pnpm

Common commands

- Generate codebase structure report:
  - uv run python scripts/scan-structure.py -f md json
  - Outputs docs/codebase-structure.md and docs/codebase-structure.json
- Run local quality gates (will expect server/ and client/ to exist):
  - ./scripts/quality-check.sh --all
  - ./scripts/quality-check.sh --backend --fix
  - ./scripts/quality-check.sh --frontend --verbose

## Documentation map

Start here:

- docs/core/00-overview.md — entry point and methodology
- docs/core/01-product.md — vision, goals, audience, PRDs
- docs/core/02-structure.md — architecture and repository structure
- docs/core/03-tech.md — technology stack and decisions
- docs/core/04-rules.md — zero-tolerance quality and workflow rules
- docs/core/05-requirements.md — EARS requirements and traceability
- docs/core/06-design.md — UX/UI, DB schema, API design
- docs/core/07-tasks.md — work breakdown and planning
- docs/core/08-workflows.md — development and CI/CD workflows
- docs/core/09-release.md — release and rollback playbook

Operational guides:

- docs/deployment/README.md — deployment playbook template
- docs/operations/README.md — operations runbook template
- docs/tasks/README.md — task management template

## Scripts

quality-check.sh

- Orchestrates backend and frontend checks with helpful flags
- Backend (expects server/): Ruff format/check, MyPy, Bandit, Pytest
- Frontend (expects client/): tsc, Prettier, next lint, Vitest, build
- Examples:
  - ./scripts/quality-check.sh
  - ./scripts/quality-check.sh --backend --fix
  - ./scripts/quality-check.sh --frontend --verbose

scan-structure.py

- Scans the repository and exports structure to docs/
- Usage: uv run python scripts/scan-structure.py -f md json

## Suggested next steps

- Duplicate this template for a new project
- Create server/ (backend) and client/ (frontend) scaffolds
- Fill out docs/core in order and keep them up to date
- Wire CI to enforce the rules in docs/core/04-rules.md and mirror scripts/quality-check.sh
