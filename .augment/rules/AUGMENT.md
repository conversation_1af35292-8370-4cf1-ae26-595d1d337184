---
type: "manual"
description: "At the start of EVERY task - this is not optional."---
# AUGMENT.md

This file provides **strict and non-negotiable guidance** to AUGMENT Code when working with code in this repository. All
instructions and references herein are **mandatory** and supersede any internal assumptions.

## Project Overview

The projects in this repository serve as templates for future projects.

## Project Guidelines Reference (AUTHORITATIVE SOURCE)

This project follows comprehensive instructions for creating guidelines documented in the `docs/core/` folder. **These documents will serve as the single source
of truth for all project decisions, standards, and methodologies.**

- **docs/core/overview.md**: Project overview and documentation structure
- **docs/core/product.md**: Product vision, goals, and target audience
- **docs/core/structure.md**: Project structure and architectural patterns
- **docs/core/tech.md**: Technology stack specification and tool versions
- **docs/core/rules.md**: **CRITICAL: Development standards and quality gates (Mandatory Adherence)**
- **docs/core/requirements.md**: Functional and non-functional requirements
- **docs/core/design.md**: Technical architecture and design decisions
- **docs/core/tasks.md**: Implementation task breakdown
- **docs/core/workflows.md**: Development and deployment workflows
- **docs/core/release.md**: Release management and handover documentation

**ABSOLUTELY CRITICAL**:

- **NEVER DEVIATE** from the standards, policies, and methodologies documented in `docs/core/rules.md`.
- **ZERO EXCEPTIONS** will be made for linting errors, type safety violations, test failures, or any form of technical
  debt.
- All code must reflect **immaculate attention to detail** and adhere to professional electrical design standards.

---

## CI/CD Quality Gates (PRIMARY ENFORCEMENT)

**CRITICAL**: The project uses **automated quality enforcement** through GitHub Actions CI/CD pipelines. Quality gates
run automatically on every Pull Request and prevent merging non-compliant code.

### Local Development Quality Checks

```bash
# Run comprehensive quality checks locally
./scripts/quality-check.sh

# Backend checks with auto-fix
./scripts/quality-check.sh --backend --fix

# Frontend checks with verbose output
./scripts/quality-check.sh --frontend --verbose
```

### Quality Gate Components

**CI/CD Quality Gates** (GitHub Actions):

- **Backend Quality Gates**: Ruff formatting/linting, MyPy type checking, Bandit security scanning, Unit/Integration
  tests
- **Frontend Quality Gates**: Prettier formatting, ESLint linting, TypeScript compilation, Unit tests, Build
  verification
- **End-to-End Tests**: Full application testing with database and services
- **Branch Protection**: Prevents merging PRs with failing quality gates

**Development Workflow**:

1. Create feature branch and make changes
2. Run local quality checks: `./scripts/quality-check.sh`
3. Push branch and create Pull Request
4. GitHub Actions automatically run all quality gates
5. Fix any failing checks - PR cannot merge until all pass
6. Merge when all quality gates pass and PR is approved

### Emergency Override (Local Development Only)

```bash
# Bypass local checks (CI will still enforce)
git commit --no-verify -m "Emergency commit - will fix in PR"
```

## Manual Development Commands (FOR REFERENCE)

### Backend (Server)

```bash
# Run uv commands from server/
cd ~/dev/templates/server/
# Start the backend server
uv run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# Manual quality checks (automated via pre-commit hooks)
uv run mypy src/ --show-error-codes
uv run ruff format .
uv run ruff format . --check
uv run bandit -r src/ -f json -o bandit-report.json

# Testing (MUST PASS WITH REQUIRED COVERAGE)
uv run pytest --html=test-report-all.html --self-contained-html
uv run pytest -v -m "not integration and not performance" --html=test-report-unit.html --self-contained-html
uv run pytest -v -m tests/integration --html=test-report-integration.html --self-contained-html
uv run pytest -v -m tests/performance --html=test-report-performance.html --self-contained-html
uv run pytest tests/ --cov=src --cov-report=term-missing --cov-report=xml --html=test-report-cov.html --self-contained-html

# Test Logging (AUTOMATIC)
# All pytest runs automatically generate dual logs in server/test_logs/:
# - failed_tests.log: Summary of failed tests with quick overview
# - failed_tests_verbose.log: Detailed failure information with full stack traces
# Console output uses dot-mode for clean test execution display

# Database
# Run alembic commands from server/src/
cd ~/dev/templates/server/src/
uv run alembic check
uv run alembic version
uv run alembic current
uv run alembic upgrade head

# Run uv commands from server/
cd ~/dev/templates/server/
# Wipe database
uv run python main.py wipe-database --confirm       # WARNING: This will DELETE your development database and re-migrate.
# Seed database
uv run python main.py seed-general-data         # Seed database with Phase 1 data (src/core/models/general/)

# Documentation
uv run mkdocs build --clean --site-dir site
```

### Frontend (Client)

```bash
# Run pnpm commands from client/
cd ~/dev/templates/client/
# Start the frontend server
pnpm run dev

# Manual quality checks (automated via pre-commit hooks)
pnpm tsc --noEmit
pnpm next lint --fix
pnpm prettier --write --log-level=warn \"**/*.{ts,tsx,mdx}\" --cache

# Testing (MUST PASS WITH REQUIRED COVERAGE)
pnpm vitest [source] --run
pnpm vitest [source] --coverage --run
pnpm playwright test tests/e2e/[source]
```

---

## Key Architectural Patterns (MANDATORY ADOPTION)

### CRUD Endpoint Factory Pattern

**CRITICAL**: Use the CRUD endpoint factory for all new entities to avoid boilerplate code. This is a unified pattern
and its usage is **mandatory** unless explicitly documented otherwise.

```python
# For new entities, use the factory pattern
from src.core.utils.crud_endpoint_factory import create_simple_crud_router

# Create CRUD router
crud_router = create_simple_crud_router(
    entity_name="your_entity",
    entity_name_plural="your_entities",
    create_schema=YourCreateSchema,
    read_schema=YourReadSchema,
    update_schema=YourUpdateSchema,
    list_response_schema=YourListResponseSchema,
    service_class=YourService,
    service_dependency=get_your_service,
    id_type=int,
    searchable_fields=["name", "description"],
    sortable_fields=["name", "created_at", "updated_at"],
)
```

---

## Testing Strategy (MANDATORY ADHERENCE)

**All testing requirements, standards, and procedures are documented in the authoritative testing documentation:**

📋 **[TESTING.md](docs/TESTING.md)** - Complete Testing Strategy & Implementation Guide

This comprehensive document contains:

- **Testing Standards & Requirements** (coverage, pass rates, quality gates)
- **Development Commands** for backend (uv) and frontend (pnpm)
- **Backend & Frontend Testing Strategies** (architecture, patterns, best practices)
- **Testing Workflows** (5-phase methodology, CI/CD integration)
- **Infrastructure & Achievements** (historical context, resolved issues)

**Mandatory Requirements:**

- **Automated quality enforcement** via CI/CD pipelines (GitHub Actions)
- **100% pass rate** for all tests before commits
- **Coverage targets** as specified in TESTING.md
- **Zero tolerance** for test failures in main branch
- **Automated test logging** - dual logs track all test runs and failures
- **Zero manual quality verification** - automation prevents non-compliant commits

**Test Logging System:**

- **Automatic Logging**: All pytest runs generate logs in `server/test_logs/`
- **Summary Log**: `failed_tests.log` provides quick overview of failed tests
- **Verbose Log**: `failed_tests_verbose.log` contains detailed failure information
- **Console Output**: Dot-mode display for clean test execution without verbose spam
- **Scope Detection**: Automatically identifies test scope (specific files, markers, etc.)

---

## Development Standards (STRICTLY ENFORCED)

**Adherence to these standards is paramount and non-negotiable for every single line of code.**

1. **Robust design principles:** Apply **SOLID** principles for structural design, ensuring maintainability and
   flexibility through focused responsibilities, extensibility, and proper abstraction. Complement these with practices
   like **DRY**, **KISS**, and **TDD** to streamline implementation, reduce complexity, and enhance overall code
   quality. **No exceptions.**
2. **5-Phase Methodology:** Adopt a systematic 5-phase approach for each feature or task. This structured,
   quality-driven development process is **mandatory** for all work.
   1. **Discovery & Analysis:** Understand the current state of the system, identify requirements, and define the scope
      of the main task.
   2. **Task Planning:** Break down tasks into smaller, manageable units (max 30-minute work batches) to ensure
      efficient progress.
   3. **Implementation:** Execute changes with **engineering-grade quality**, focusing on unified patterns and
      professional electrical design standards.
   4. **Verification:** Ensure all requirements are met through comprehensive testing and **automated quality gate
      compliance** (pre-commit hooks prevent non-compliant code).
   5. **Documentation & Handover:** Prepare comprehensive documentation and create a handover package for future
      development and AI agent transfer.
3. **Unified Patterns:** Apply consistent "unified patterns" for calculations, service layers, and repositories. Utilize
   decorators for error handling, performance monitoring, and memory optimization as specified in `docs/core/design.md`.
   **Consistency is key.**
4. **Quality & Standards Focus:** Ensure **immaculate attention to detail**. Adhere to professional electrical design
   standards (IEC/EN). Maintain **complete type safety** with MyPy validation (100% compliance) and comprehensive
   testing (including real database connections where appropriate).
5. **Key Success Metrics:** Define success through high unified patterns compliance (≥90%), extensive test coverage
   (≥85% overall, 100% for critical logic), 100% test pass rates, and zero remaining placeholder implementations.
   **These are minimum success criteria.**

---

## Module Relationships (UNDERSTAND AND RESPECT)

### Backend Layer Dependencies

1. **API Layer** → **Services Layer** → **Repositories Layer** → **Models Layer**
2. **Unified Error Handling** spans all layers.
3. **Security Validation** integrated at API and Service layers.
4. **Performance Monitoring** integrated at all layers.

### Frontend Module Dependencies

1. **App Router** → **Modules** → **Components** → **UI Primitives**
2. **State Management** (Zustand) for client state.
3. **React Query** for server state management.
4. **API Client** for backend communication.

### Cross-System Integration

- **Authentication Flow**: Frontend auth module ↔ Backend auth services
- **Component Management**: Frontend component module ↔ Backend component services
- **Real-time Updates**: WebSocket connections for live collaboration
- **File Uploads**: Direct integration for component specifications and drawings

---

## Performance Optimization

- **Backend**: Use performance monitoring decorators on services. Implement efficient queries and caching strategies.
- **Frontend**: Implement React Query for server state caching, code splitting, and image optimization.
- **Database**: Leverage existing indexing and ensure query optimization.
- **Caching**: Utilize the built-in caching middleware.

---
